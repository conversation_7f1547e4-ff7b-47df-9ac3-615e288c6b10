import { Card } from "primereact/card";
import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "primereact/button";
import { Menu } from "primereact/menu";
import FirstBarDemo from "./FirstBarDemo";
import SecondPieDemo from "./SecondPieDemo";
import ThirdLineDemo from "./ThirdLineDemo";
import SubGraph2Demo from "./SubGraph2Demo";
import SubGraph3Demo from "./SubGraph3Demo";
import SubGraph4Demo from "./SubGraph4Demo";
import SubGraph5Demo from "./SubGraph5Demo";
import SupplierTable from "./SupplierTable";
import SubGraph1Demo from "./SubGraph1Demo";
import FiltersSectionDemo from "./FiltersSectionDemo";
// import supplyData from "./supply.json";
import { API } from "../../../../../constants/api_url";
import APIServices from "../../../../../service/APIService";
import AggregatedRadarChart from "./AggregatedRadarChart";

const SuppliersDashboard = () => {
  const [data, setData] = useState([]);

  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedbu, setSelectedBU] = useState('All');

  const [supplyData, setSupplyData] = useState([]);
  const [filteredSupplyData, setFilteredSupplyData] = useState([]);
  const [selfAssessmentData, setSelfAssessmentData] = useState([]);
  const [latestSelfAssessmentScore, setLatestSelfAssessmentScore] = useState(null);

  useEffect(() => {
    getSupplyData();
    getSelfAssessmentData();
  }, []);

  const getSupplyData = async () => {
    try {
      const response = await APIServices.get(API.supplierResponse);
      if (response.data) {
        console.log(response.data, ' R D')
        setSupplyData(response.data);
      }
    } catch (error) {
      console.log("Error fetching supply data: ", error);
    }
  }

  const getSelfAssessmentData = async () => {
    try {
      const uriString = {
        "include": [
          {
            "relation": "vendor"
          }
        ]
      };

      const response = await APIServices.get(
        API.SupplierAssessmentAss_Up_All + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
      );

      console.log('Self Assessment API Response:', response);
      console.log('Self Assessment Data:', response.data);

      if (response.data && Array.isArray(response.data)) {
        setSelfAssessmentData(response.data);
      }
    } catch (error) {
      console.error('Error fetching self-assessment data:', error);
    }
  }

  // Function to calculate latest self-assessment score based on filtered data
  const calculateLatestSelfAssessmentScore = (filteredData, assessmentData) => {
    if (!filteredData.length || !assessmentData.length) {
      return null;
    }

    // Get vendor codes from filtered supply data
    const filteredVendorCodes = new Set(
      filteredData.map(item => item.vendor_code?.toString()).filter(Boolean)
    );

    // Filter self-assessment data for vendors in filtered supply data
    const relevantAssessments = assessmentData.filter(assessment =>
      assessment.vendor &&
      assessment.vendor.code &&
      assessment.supplierMSIScore &&
      filteredVendorCodes.has(assessment.vendor.code.toString())
    );

    if (!relevantAssessments.length) {
      return null;
    }

    // Group assessments by vendor code
    const groupedByVendor = {};
    relevantAssessments.forEach(assessment => {
      const vendorCode = assessment.vendor.code.toString();
      if (!groupedByVendor[vendorCode]) {
        groupedByVendor[vendorCode] = [];
      }
      groupedByVendor[vendorCode].push(assessment);
    });

    // Get latest assessment for each vendor and collect scores
    const latestScores = [];
    Object.keys(groupedByVendor).forEach(vendorCode => {
      const vendorAssessments = groupedByVendor[vendorCode];

      // Sort by created_on date (most recent first) and get the latest
      const latestAssessment = vendorAssessments.sort((a, b) =>
        new Date(b.created_on) - new Date(a.created_on)
      )[0];

      latestScores.push(parseFloat(latestAssessment.supplierMSIScore));
    });

    // Calculate average of latest scores
    if (latestScores.length === 0) {
      return null;
    }

    const averageScore = latestScores.reduce((sum, score) => sum + score, 0) / latestScores.length;

    console.log('Latest scores by vendor:', latestScores);
    console.log('Average of latest scores:', averageScore);

    return averageScore;
  };

  // Function to get MSI grade based on calibration score
  const getMSIGrade = (score) => {
    if (score >= 86) return "Platinum";
    if (score >= 71) return "Gold";
    if (score >= 56) return "Silver";
    return "Not Met";
  };

  const menuRef = useRef(null);
  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];

  useEffect(() => {
    if (supplyData.length > 0) {
      // Rank the data initially (without filtering)
      let rankedData = [...supplyData].sort(
        (a, b) => b.msi_score - a.msi_score
      );

      rankedData = rankedData.map((item, index) => ({
        ...item,
        rank: index + 1, // Rank starts from 1
      }));

      setData(rankedData); // Store the ranked data in the state
      setFilteredSupplyData(rankedData); // Set the initial filtered data to the ranked data
    }
  }, [supplyData]);

  useEffect(() => {
    // Apply filter logic here, if any
    if (!data?.length) return;

    // Example: Apply filter (if any)
    // let filteredData = data.filter(item => item.someCondition);
    // setFilteredSupplyData(filteredData); // Update filtered data
  }, [data]);

  // Update latest self-assessment score when filtered data or self-assessment data changes
  useEffect(() => {
    const latestScore = calculateLatestSelfAssessmentScore(filteredSupplyData, selfAssessmentData);
    setLatestSelfAssessmentScore(latestScore);
    console.log('Latest Self Assessment Score for filtered data:', latestScore);
  }, [filteredSupplyData, selfAssessmentData]);

  return (
    <>
      <FirstBarDemo supplyData={supplyData} />
      <SecondPieDemo supplyData={supplyData} />
      <ThirdLineDemo supplyData={supplyData} />

      <Card>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <h3>MSI Performance Analytics</h3>
          <div
            style={{
              margin: "18px 10px 18px 10px",
              display: "flex",
            }}
          >
            <div
              className="buttons"
              style={{
                background: "#F0F2F4",
                borderRadius: "3px",
                width: "4.5rem",
                marginLeft: "10px",
                height: "30px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Button
                style={{
                  background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                  marginRight: "4px",
                }}
                onClick={() => {
                  setActiveMode(false);
                }}
              >
                <i className="pi pi-table fs-19" />
              </Button>
              <Button
                style={{
                  background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                }}
                onClick={() => {
                  setActiveMode(true);
                }}
              >
                <i className="pi pi-chart-bar fs-19" />
              </Button>
            </div>
            <div ref={menuRef}>
              <Button
                style={{
                  color: "black",
                  height: "30px",
                  marginLeft: "3px",
                  background: "#F0F2F4",
                  border: "0px",
                  padding: "6px",
                  position: "relative",
                }}
                onClick={() => {
                  setDropdownOpen(!dropdownOpen);
                }}
              >
                <i className="pi pi-angle-down fs-19" />
              </Button>
              {dropdownOpen && (
                <Menu
                  model={panelItems}
                  style={{
                    position: "absolute",
                    right: 45,
                    zIndex: "1",
                    padding: 0,
                  }}
                ></Menu>
              )}
            </div>
          </div>
        </div>
        <FiltersSectionDemo
          supplyData={supplyData} setSelectedBu={(e) => { setSelectedBU(e) }}
          setFilteredSupplyData={setFilteredSupplyData}
        />
        <Card style={{ height: "120px", backgroundColor: "#FAFAFA" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-evenly",
            }}
          >
            <h3 style={{ marginLeft: "-180px" }}>Summary</h3>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>
                Self Assessment Score
              </p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                  color: "#F59E0B"
                }}
              >
                {latestSelfAssessmentScore !== null
                  ? latestSelfAssessmentScore.toFixed(2)
                  : "N/A"}
              </p>
            </div>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>
                MSI Calibration Score
              </p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                }}
              >
                {filteredSupplyData.length
                  ? filteredSupplyData[0].msi_score
                  : "N/A"}
              </p>
            </div>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <p style={{ fontSize: "18px", fontWeight: 600 }}>MSI Grade</p>
              <p
                style={{
                  fontSize: "20px",
                  fontWeight: 700,
                  textAlign: "center",
                  color: filteredSupplyData.length === 1 && filteredSupplyData[0].msi_score
                    ? getMSIGrade(filteredSupplyData[0].msi_score) === "Platinum" ? "#E5E7EB"
                    : getMSIGrade(filteredSupplyData[0].msi_score) === "Gold" ? "#F59E0B"
                    : getMSIGrade(filteredSupplyData[0].msi_score) === "Silver" ? "#9CA3AF"
                    : "#EF4444"
                    : "#6B7280"
                }}
              >
                {filteredSupplyData.length === 1 && filteredSupplyData[0].msi_score
                  ? getMSIGrade(filteredSupplyData[0].msi_score)
                  : "N/A"}
              </p>
            </div>
          </div>
        </Card>
          <div className="row mt-3">
              <div className="p-2 border card col-md-6">
                <SubGraph1Demo supplyData={filteredSupplyData} />
                 </div>
               <div className="p-2 border card col-md-6" style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                <SubGraph2Demo selectedSite={selectedbu} supplyData={filteredSupplyData} />
              </div>
              </div>
               <div className="row">
           <div className="p-2 border card col-md-12">
  <AggregatedRadarChart filteredSupplyData={filteredSupplyData} />
    </div>
    </div>
{/*
        <div style={{ display: "flex", marginTop: "30px", maxWidth: "100%" }}>
          <SubGraph1Demo supplyData={filteredSupplyData} />

          <SubGraph2Demo selectedSite={selectedbu} supplyData={filteredSupplyData} />
        </div>
        <div style={{
          display: "flex",
          width: "100%",
          margin: "30px auto",
          justifyContent: "center",
          alignItems: "center"
        }}>
          <AggregatedRadarChart filteredSupplyData={filteredSupplyData} />
        </div> */}

      </Card>
      {/* <Card>
        <div style={{ display: "flex" }}>
          <SubGraph3Demo supplyData={filteredSupplyData} />
          <SubGraph4Demo supplyData={filteredSupplyData} />
          <SubGraph5Demo supplyData={filteredSupplyData} />
        </div>
      </Card> */}
       <div className="row mt-3">
              <div className="p-2 border card col-md-4">
              <SubGraph3Demo supplyData={filteredSupplyData} />
              </div>
              <div className="p-2 border card col-md-4">
              <SubGraph4Demo supplyData={filteredSupplyData} />
              </div>
              <div className="p-2 border card col-md-4">
              <SubGraph5Demo supplyData={filteredSupplyData} />
              </div>
            </div>


       <div className="row">
           <div className="p-2 border card col-md-12" style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
    <SupplierTable supplyData={filteredSupplyData} />
</div>
              </div>

    </>
  );
};

export default SuppliersDashboard;
