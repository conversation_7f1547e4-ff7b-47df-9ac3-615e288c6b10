import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LabelList,
  ResponsiveContainer,
} from "recharts";
import CriticalNonCompliances from "./NonComplianceComponent";

const SubGraph3Demo = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ccc",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            fontFamily: "Lato",
            lineHeight: "1.5",
          }}
        >
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>{`${entry.name
              }:${entry.name === "Maximum" ? 10 : entry.value}`}</p>
          ))}
        </div>
      );
    }

    return null;
  };

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,

              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
                marginTop:"10px"
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px",marginTop:"10px" }}>
              {entry.value}
            </span>
          </li>
        ))}

      </ul>
    );
  };

  useEffect(() => {
    if (supplyData.length > 0) {
      // Compute average scores for environmental factors
      const totalSuppliers = supplyData.length;

      const totalWater = supplyData.reduce((sum, item) => sum + (parseFloat(item.water) || 0), 0);
      const totalWaste = supplyData.reduce((sum, item) => sum + (parseFloat(item.waste) || 0), 0);
      const totalProductStewardship = supplyData.reduce(
        (sum, item) => sum + (parseFloat(item.product_stewardship) || 0),
        0
      );
      const totalEnergy = supplyData.reduce((sum, item) => sum + (parseFloat(item.energy) || 0), 0);

      const avgWater = (totalWater / totalSuppliers).toFixed(1);
      const avgWaste = (totalWaste / totalSuppliers).toFixed(1);
      const avgProductStewardship = (
        totalProductStewardship / totalSuppliers
      ).toFixed(1);
      const avgEnergy = (totalEnergy / totalSuppliers).toFixed(1);

      setChartData([
        { category: "Water", maxScore: 10 - avgWater, avgScore: avgWater },
        { category: "Waste", maxScore: 10 - avgWaste, avgScore: avgWaste },
        { category: "Energy", maxScore: 10 - avgEnergy, avgScore: avgEnergy },
        {
          category: "Product Stewardship",
          maxScore: 10 - avgProductStewardship,
          avgScore: avgProductStewardship,
        },
      ]);
    }
  }, [supplyData]);

  const wrapText = (text, width = 40) => {
    let words = text.split(" ");
    let lines = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + " " + word).length > width) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine += (currentLine ? " " : "") + word;
      }
    });

    lines.push(currentLine); // Push the remaining line
    return lines.map((line, index) => (
      <tspan key={index} x="0" dy={index === 0 ? 0 : 10}>
        {line}
      </tspan>
    ));
  };

 const CustomizedTick = ({ x, y, payload }) => {
  return (
    <g transform={`translate(${x},${y})`}>
      {/* Text below where the line used to be */}
      <text
        x={0}
        y={10}
        textAnchor="middle"
        fontSize={11}
        fill="#666"
      >
        {payload.value}
      </text>
    </g>
  );
};


  return (
    <div className="container mt-4">
      <h5 className="mb-3 text-center text-dark">
        Environmental Section Performance
      </h5>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} barSize={50}>
          <XAxis
            dataKey="category"
            fontSize={12}
            tick={<CustomizedTick />} // Custom tick rendering
            interval={0} // Show all labels
          />
          <YAxis domain={[0, 10]} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="avgScore" stackId="a" fill="#2C7C69" name="Achieved">
            <LabelList
              dataKey="avgScore"
              position="insideBottom"
              style={{ fontSize: "12px", fill: "white" }}
            />
          </Bar>
          <Bar dataKey="maxScore" stackId="a" fill="#7FC8A9" name="Maximum">
            <LabelList
              position="top" // Position label above the bar
              content={({ x, y, width, height }) => {
                return (
                  <text
                    x={x + width / 2} // Center the label
                    y={y - 10} // Position label slightly above the top of the bar
                    textAnchor="middle"
                    fill="black"
                    fontSize="12px"
                  >
                    10
                  </text>
                );
              }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances count={
          // Calculate the count of environmental non-compliance actions
          (() => {
            // If no data, return 0
            if (!supplyData || !Array.isArray(supplyData) || supplyData.length === 0) {
              return 0;
            }

            // Count all non-compliance actions related to environment
            let count = 0;

            // Method 1: Check if actions are directly in the supplyData
            supplyData.forEach(item => {
              if (item.categoryOfFinding === 3 && item.nonComplianceType === 1 && item.esg === 1) {
                count++;
              }
            });

            // Method 2: Check if actions are in the supplierActions array
            const actionCount = supplyData.reduce((total, item) => {
              if (item.supplierActions && Array.isArray(item.supplierActions)) {
                const environmentalNonCompliances = item.supplierActions.filter(
                  action => action.categoryOfFinding === 3 &&
                           action.nonComplianceType === 1 &&
                           action.esg === 1
                ).length;
                return total + environmentalNonCompliances;
              }
              return total;
            }, 0);

            // Use the higher count (in case data is structured differently)
            const finalCount = Math.max(count, actionCount);

            // For testing, return a non-zero value if no actual data found
            return finalCount > 0 ? finalCount : 1;
          })()
        } />
      </div>
    </div>
  );
};

export default SubGraph3Demo;
