import React, { useState, useRef, useEffect, useMemo, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { But<PERSON> } from "primereact/button";
import { useSelector } from "react-redux";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { ChartSortDropdown, createSortOptions, sortData } from "./components/ChartSortDropdown";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";
// Default data if no supplyData is provided
const defaultData = [
  { month: "Apr", selfAssessment: null, calibrationScore: null },
  { month: "May", selfAssessment: null, calibrationScore: null },
  { month: "Jun", selfAssessment: null, calibrationScore: 48.3 },
  { month: "Jul", selfAssessment: null, calibrationScore: 48.3 },
  { month: "Aug", selfAssessment: null, calibrationScore: 42.19 },
  { month: "Sep", selfAssessment: null, calibrationScore: 48.54 },
  { month: "Oct", selfAssessment: null, calibrationScore: 49.04 },
  { month: "Nov", selfAssessment: null, calibrationScore: 51.6 },
  { month: "Dec", selfAssessment: null, calibrationScore: 52.9 },
  { month: "Jan", selfAssessment: null, calibrationScore: 52.9 },
  { month: "Feb", selfAssessment: null, calibrationScore: null },
  { month: "Mar", selfAssessment: null, calibrationScore: null },
];

const CustomLegend = (props) => {
  const { payload } = props;
  return (
    <ul
      style={{
        display: "flex",
        listStyleType: "none",
        justifyContent: "center",
        padding: "10px",
        margin: "10px 0",
        backgroundColor: "#f8f9fa",
        borderRadius: "5px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      {payload.map((entry, index) => (
        <li
          key={`item-${index}`}
          style={{
            display: "flex",
            alignItems: "center",
            marginRight: "20px",
            padding: "5px 10px",
          }}
        >
          <span
            style={{
              backgroundColor: entry.color,
              marginRight: "8px",
              width: "15px",
              height: "15px",
              borderRadius: "3px",
              display: "inline-block",
            }}
          ></span>
          <span style={{ color: "#333", fontSize: "14px", fontWeight: "500" }}>
            {entry.dataKey === "selfAssessment"
              ? "Self Assessment Score"
              : "MSI Calibration Score"}
          </span>
        </li>
      ))}
    </ul>
  );
};

const ThirdLineDemo = ({ supplyData = [] }) => {

let uriString = {

          
            "include": [
               
                {

                    "relation": "vendor"
                },
               
            ]

        };
  const promise1 = APIServices.get(API.SupplierAssessmentAss_Up_All + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`)


  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);
  const [visibleSeries, setVisibleSeries] = useState({
    selfAssessment: true,
    calibrationScore: true,
  });

  // State for chart data
  const [chartData, setChartData] = useState(defaultData);

  // Sorting state
  const [sortField, setSortField] = useState('none');
  const [sortOrder, setSortOrder] = useState(1); // 1 for ascending, -1 for descending

  // Memoize sorting options to prevent re-creation
  const sortOptions = useMemo(() => createSortOptions([
    { name: 'month', label: 'Month', type: 'string' },
    { name: 'selfAssessment', label: 'Self Assessment', type: 'number' },
    { name: 'calibrationScore', label: 'Calibration Score', type: 'number' },
  ]), []);

  // Handle sort change - memoized to prevent re-creation
  const handleSortChange = useCallback((e) => {
    const selectedSort = e.value;
    const [, direction] = selectedSort.split('_');

    setSortField(selectedSort);
    setSortOrder(direction === 'asc' ? 1 : -1);

    // Apply sorting to the data
    setChartData(prevData => sortData(prevData, selectedSort));
  }, []);

  // Memoize Line component props to prevent re-creation
  const lineProps = useMemo(() => ({
    selfAssessment: {
      type: "linear",
      dataKey: "selfAssessment",
      stroke: "#F59E0B",
      activeDot: { r: 6 },
      dot: { r: 4 },
      strokeWidth: 3,
      connectNulls: true,
      name: "Self Assessment Score"
    },
    calibrationScore: {
      type: "linear",
      dataKey: "calibrationScore",
      stroke: "#6C480B",
      activeDot: { r: 6 },
      dot: { r: 4 },
      strokeWidth: 3,
      connectNulls: true,
      name: "MSI Calibration Score"
    }
  }), []);

  // Process supplyData to generate chart data
  useEffect(() => {
    if (supplyData && supplyData.length > 0) {
      // Process the supplyData to extract monthly data
      try {
        // Group data by month-year and calculate average scores
        const monthlyData = {};
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        // Process each supplier data
        supplyData.forEach(supplier => {
          // Extract month from audit_start_date if available
          if (supplier.audit_start_date) {
            // Parse the date format "24.08.2024" (DD.MM.YYYY)
            const dateParts = supplier.audit_start_date.split('.');
            if (dateParts.length === 3) {
              const day = parseInt(dateParts[0]);
              const monthIndex = parseInt(dateParts[1]) - 1; // Month is 0-indexed in JavaScript
              const year = parseInt(dateParts[2]);

              const date = new Date(year, monthIndex, day);
              const monthName = months[date.getMonth()];
              const monthYearKey = `${monthName} ${year}`;

              // Initialize monthly data if not exists
              if (!monthlyData[monthYearKey]) {
                monthlyData[monthYearKey] = {
                  month: monthYearKey,
                  monthName: monthName,
                  year: year,
                  selfAssessment: null,
                  calibrationScore: null,
                  count: 0,
                  selfAssessmentCount: 0,
                  selfAssessmentTotal: 0
                };
              }

              // Add calibration score if available
              if (supplier.msi_score) {
                if (!monthlyData[monthYearKey].calibrationScore) {
                  monthlyData[monthYearKey].calibrationScore = 0;
                }
                monthlyData[monthYearKey].calibrationScore += parseFloat(supplier.msi_score);
                monthlyData[monthYearKey].count++;
              }

              // Add self assessment score if available (if this field exists in your data)
              if (supplier.self_assessment_score) {
                monthlyData[monthYearKey].selfAssessmentTotal += parseFloat(supplier.self_assessment_score);
                monthlyData[monthYearKey].selfAssessmentCount++;
              }
            }
          }
        });
        // Calculate averages
        const processedData = Object.values(monthlyData).map(item => {
          return {
            month: item.month,
            monthName: item.monthName,
            year: item.year,
            selfAssessment: item.selfAssessmentCount > 0 ?
              parseFloat(item.selfAssessmentTotal / item.selfAssessmentCount).toFixed(2) : null,
            calibrationScore: item.count > 0 ?
              parseFloat(item.calibrationScore / item.count).toFixed(2) : null
          };
        });

        // Sort by year and then by month order
        const sortedData = processedData.sort((a, b) => {
          if (a.year !== b.year) {
            return a.year - b.year;
          }
          return months.indexOf(a.monthName) - months.indexOf(b.monthName);
        });

        // Use hardcoded data for testing if no real data is available
        if (sortedData.every(item => item.calibrationScore === null && item.selfAssessment === null)) {
          // Create some sample data for testing
          const sampleData = [...defaultData];

          // Make sure at least some months have data for calibration scores
          sampleData[5].calibrationScore = "48.54"; // June
          sampleData[6].calibrationScore = "49.04"; // July
          sampleData[7].calibrationScore = "51.60"; // August
          sampleData[8].calibrationScore = "52.90"; // September

          // Add sample self-assessment scores
          sampleData[4].selfAssessment = "45.20"; // May
          sampleData[5].selfAssessment = "46.75"; // June
          sampleData[6].selfAssessment = "47.30"; // July
          sampleData[7].selfAssessment = "49.80"; // August
          sampleData[8].selfAssessment = "50.40"; // September

          setChartData(sampleData);
        } else {


          setChartData(sortedData);
        }
      } catch (error) {
        console.error("Error processing supply data for chart:", error);
        setChartData(defaultData);
      }
    } else {
      // Create some sample data for testing
      const sampleData = [...defaultData];

      // Make sure at least some months have data for calibration scores
      sampleData[5].calibrationScore = "48.54"; // June
      sampleData[6].calibrationScore = "49.04"; // July
      sampleData[7].calibrationScore = "51.60"; // August
      sampleData[8].calibrationScore = "52.90"; // September

      // Add sample self-assessment scores
      sampleData[4].selfAssessment = "45.20"; // May
      sampleData[5].selfAssessment = "46.75"; // June
      sampleData[6].selfAssessment = "47.30"; // July
      sampleData[7].selfAssessment = "49.80"; // August
      sampleData[8].selfAssessment = "50.40"; // September

      setChartData(sampleData);
    }
  }, [supplyData]);

  // Toggle visibility of series
  const toggleSeriesVisibility = (seriesName) => {
    setVisibleSeries(prev => ({
      ...prev,
      [seriesName]: !prev[seriesName]
    }));
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // Implement Excel export functionality
            if (activeMode) {
              alert("Exporting chart data to Excel...");
              // Actual implementation would go here
            } else {
              // Export table data
              alert("Exporting table data to Excel...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            if (activeMode && chartRef.current) {
              alert("Exporting chart as PDF...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            if (activeMode && chartRef.current) {
              alert("Exporting chart as JPG...");
              // Actual implementation would go here
            }
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            if (chartRef.current) {
              alert("Printing chart...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: visibleSeries.selfAssessment ? "Hide Self Assessment" : "Show Self Assessment",
          icon: visibleSeries.selfAssessment ? "pi pi-eye-slash" : "pi pi-eye",
          command: () => toggleSeriesVisibility("selfAssessment"),
        },
        {
          label: visibleSeries.calibrationScore ? "Hide Calibration Score" : "Show Calibration Score",
          icon: visibleSeries.calibrationScore ? "pi pi-eye-slash" : "pi pi-eye",
          command: () => toggleSeriesVisibility("calibrationScore"),
        },
      ],
    },
  ];

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px", fontWeight: "600" }}>
          12 Month Trend in Supplier Self-Assessment and MSI Calibration Scores
          (2024-25)
        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          {/* Sort Dropdown */}
          <ChartSortDropdown
            value={sortField}
            options={sortOptions}
            onChange={handleSortChange}
          />

          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-evenly",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px", fontWeight: "600" }}>Self Assessment Score</p>
          <p style={{ fontSize: "20px", fontWeight: "700", color: "#F59E0B" }}>
            {chartData.some(item => item.selfAssessment)
              ? chartData.filter(item => item.selfAssessment).reduce((max, item) => {
                const score = parseFloat(item.selfAssessment || 0);
                return score > max ? score : max;
              }, 0).toFixed(2)
              : 'N/A'}
          </p>
        </div>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px", fontWeight: "600" }}>MSI Calibration Score</p>
          <p style={{ fontSize: "20px", fontWeight: "700", color: "#6C480B" }}>
            {chartData.some(item => item.calibrationScore)
              ? chartData.filter(item => item.calibrationScore).reduce((max, item) => {
                const score = parseFloat(item.calibrationScore || 0);
                return score > max ? score : max;
              }, 0).toFixed(2)
              : 'N/A'}
          </p>
        </div>{" "}
        {/* <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px" }}>Rating</p>
          <p style={{ fontSize: "20px", color: "#D28B24" }}>BRONZE</p>
        </div> */}
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={chartData} ref={chartRef}>
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend content={CustomLegend} />
            <Line
              {...lineProps.selfAssessment}
              hide={!visibleSeries.selfAssessment}
            />
            <Line
              {...lineProps.calibrationScore}
              hide={!visibleSeries.calibrationScore}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} ref={tableRef} sortMode="multiple">
            <Column field="month" header="Month" sortable />
            <Column
              field="selfAssessment"
              header="Self Assessment Score"
              sortable
              body={(rowData) => rowData.selfAssessment || 'N/A'}
            />
            <Column
              field="calibrationScore"
              header="MSI Calibration Score"
              sortable
              body={(rowData) => rowData.calibrationScore || 'N/A'}
            />
          </DataTable>
        </div>
      )}
    </Card>
  );
};

export default ThirdLineDemo;
